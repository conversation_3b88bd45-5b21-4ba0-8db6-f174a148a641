﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DAL.Migrations
{
    /// <inheritdoc />
    public partial class _3 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AuditStatus",
                table: "users",
                type: "int",
                nullable: false,
                defaultValue: 0,
                comment: "审核状态：0=待审核，1=已通过，2=已拒绝");

            migrationBuilder.AddColumn<DateTime>(
                name: "InviteTime",
                table: "users",
                type: "datetime(6)",
                nullable: true,
                comment: "邀请时间");

            migrationBuilder.AddColumn<string>(
                name: "InviterId",
                table: "users",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true,
                comment: "邀请人ID（分享人的员工ID）")
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AuditStatus",
                table: "users");

            migrationBuilder.DropColumn(
                name: "InviteTime",
                table: "users");

            migrationBuilder.DropColumn(
                name: "InviterId",
                table: "users");
        }
    }
}
