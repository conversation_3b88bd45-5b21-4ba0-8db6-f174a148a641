using Common.Caches;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using System.Text.Json;

namespace Entity.Extensions
{
    /// <summary>
    /// Video实体扩展方法
    /// 用于消除重复的实体到DTO转换逻辑
    /// </summary>
    public static class VideoExtensions
    {
        /// <summary>
        /// 将Video实体转换为VideoResponseDto
        /// </summary>
        /// <param name="video">Video实体</param>
        /// <param name="creatorName">创建者姓名（可选）</param>
        /// <returns>VideoResponseDto</returns>
        public static VideoResponseDto ToResponseDto(this Video video, string? creatorName = null)
        {
            return new VideoResponseDto
            {
                Id = video.Id,
                Title = video.Title,
                Description = video.Description,
                VideoUrl = video.VideoUrl,
                CoverUrl = video.CoverUrl,
                Duration = video.Duration,
                RewardAmount = video.RewardAmount,
                Questions = !string.IsNullOrEmpty(video.Questions)
                    ? JsonSerializer.Deserialize<List<VideoQuestionDto>>(video.Questions, MemoryCacheHelper.DefaultJsonOptions)
                    : null,
                CreatedBy = video.CreatedBy,
                CreatorName = creatorName ?? video.CreatorName ?? "未知用户",
                Status = video.Status,
                ViewCount = video.ViewCount,
                CreateTime = video.CreateTime,
                FileId = video.FileId
            };
        }

        /// <summary>
        /// 将Video实体集合转换为VideoResponseDto集合
        /// </summary>
        /// <param name="videos">Video实体集合</param>
        /// <returns>VideoResponseDto集合</returns>
        public static List<VideoResponseDto> ToResponseDtos(this IEnumerable<Video> videos)
        {
            return [.. videos.Select(video => video.ToResponseDto())];
        }

        /// <summary>
        /// 将Video实体集合转换为VideoResponseDto集合（带创建者姓名映射）
        /// </summary>
        /// <param name="videos">Video实体集合</param>
        /// <param name="creatorNameMap">创建者ID到姓名的映射</param>
        /// <returns>VideoResponseDto集合</returns>
        public static List<VideoResponseDto> ToResponseDtos(this IEnumerable<Video> videos, Dictionary<string, string> creatorNameMap)
        {
            return [.. videos.Select(video =>
            {
                var creatorName = video.CreatedBy != null && creatorNameMap.TryGetValue(video.CreatedBy, out var name)
                    ? name
                    : video.CreatorName ?? "未知用户";
                return video.ToResponseDto(creatorName);
            })];
        }
    }
}
