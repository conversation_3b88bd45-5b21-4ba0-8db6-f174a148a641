﻿// <auto-generated />
using System;
using DAL;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace DAL.Migrations
{
    [DbContext(typeof(MyContext))]
    [Migration("20250804100332_4")]
    partial class _4
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysLog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("varchar(255)")
                        .HasComment("日志ID");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("Exception")
                        .HasColumnType("longtext")
                        .HasComment("异常信息");

                    b.Property<string>("Ip")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("IP地址");

                    b.Property<string>("LogLevel")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("日志级别");

                    b.Property<string>("LogType")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("日志类型");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasComment("日志内容");

                    b.Property<string>("Method")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasComment("请求方法");

                    b.Property<string>("Operation")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("操作类型");

                    b.Property<string>("Params")
                        .HasColumnType("longtext")
                        .HasComment("请求参数");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("varchar(500)")
                        .HasComment("请求路径");

                    b.Property<long>("Time")
                        .HasColumnType("bigint")
                        .HasComment("执行时长(毫秒)");

                    b.Property<string>("UserId")
                        .HasColumnType("varchar(100)")
                        .HasComment("用户ID");

                    b.Property<string>("Username")
                        .HasColumnType("varchar(50)")
                        .HasComment("用户名");

                    b.HasKey("Id");

                    b.ToTable("sys_log");
                });

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysUser", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("varchar(255)")
                        .HasComment("用户ID");

                    b.Property<string>("AuditRemark")
                        .HasColumnType("varchar(500)")
                        .HasComment("审核备注");

                    b.Property<string>("Avatar")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasComment("头像");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("varchar(100)")
                        .HasComment("电子邮箱");

                    b.Property<string>("LastLoginIp")
                        .HasColumnType("varchar(50)")
                        .HasComment("最后登录IP");

                    b.Property<DateTime?>("LastLoginTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("最后登录时间");

                    b.Property<string>("Mobile")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasComment("手机号码");

                    b.Property<string>("ParentUserId")
                        .HasColumnType("varchar(50)")
                        .HasComment("上级用户ID");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("varchar(100)")
                        .HasComment("密码");

                    b.Property<string>("RealName")
                        .IsRequired()
                        .HasColumnType("varchar(100)")
                        .HasComment("真实姓名");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint")
                        .HasComment("用户状态");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(100)")
                        .HasComment("用户名");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint")
                        .HasComment("用户类型");

                    b.HasKey("UserId");

                    b.ToTable("sys_user");
                });

            modelBuilder.Entity("Entity.Entitys.VideoEntity.Batch", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<int>("CurrentParticipants")
                        .HasColumnType("int")
                        .HasComment("当前参与人数");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("批次描述");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("结束时间");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("批次名称");

                    b.Property<string>("Questions")
                        .HasColumnType("longtext")
                        .HasComment("问题JSON(冗余) - 格式: [{'questionText':'问题内容','orderNum':0,'options':[{'optionText':'选项1','isCorrect':true,'orderNum':0},{'optionText':'选项2','isCorrect':false,'orderNum':1}]}]");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<decimal>("RewardAmount")
                        .HasColumnType("decimal(10,2)")
                        .HasComment("红包金额");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("开始时间");

                    b.Property<byte>("Status")
                        .HasColumnType("tinyint unsigned")
                        .HasComment("状态:0下线,1上线");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<string>("VideoCoverUrl")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("封面URL(冗余)");

                    b.Property<string>("VideoDescription")
                        .HasColumnType("longtext")
                        .HasComment("视频描述(冗余)");

                    b.Property<int>("VideoDuration")
                        .HasColumnType("int")
                        .HasComment("视频时长(冗余)");

                    b.Property<int>("VideoId")
                        .HasColumnType("int")
                        .HasComment("关联视频ID");

                    b.Property<string>("VideoTitle")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("视频标题(冗余)");

                    b.Property<string>("VideoUrl")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("视频URL(冗余)");

                    b.HasKey("Id");

                    b.ToTable("batches");
                });

            modelBuilder.Entity("Entity.Entitys.VideoEntity.SystemConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConfigKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("配置键");

                    b.Property<string>("ConfigType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("配置类型");

                    b.Property<string>("ConfigValue")
                        .HasColumnType("longtext")
                        .HasComment("配置值");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("配置描述");

                    b.Property<string>("GroupName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("配置分组");

                    b.Property<byte>("IsEnabled")
                        .HasColumnType("tinyint unsigned")
                        .HasComment("是否启用:0禁用,1启用");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.ToTable("system_configs");
                });

            modelBuilder.Entity("Entity.Entitys.VideoEntity.User", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("主键ID (32位GUID字符串)");

                    b.Property<int>("AuditStatus")
                        .HasColumnType("int")
                        .HasComment("审核状态：0=待审核，1=已通过，2=已拒绝");

                    b.Property<string>("Avatar")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("头像URL");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("EmployeeId")
                        .HasColumnType("varchar(255)")
                        .HasComment("绑定的员工ID");

                    b.Property<DateTime?>("InviteTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("邀请时间");

                    b.Property<string>("InviterId")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("邀请人ID（分享人的员工ID）");

                    b.Property<DateTime?>("LastLogin")
                        .HasColumnType("datetime(6)")
                        .HasComment("最后登录时间");

                    b.Property<string>("Nickname")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("微信昵称");

                    b.Property<string>("OpenId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("微信OpenID");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<string>("UnionId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("微信UnionID");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.HasIndex("EmployeeId")
                        .HasDatabaseName("IX_Users_EmployeeId");

                    b.ToTable("users");
                });

            modelBuilder.Entity("Entity.Entitys.VideoEntity.UserAudit", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("主键ID (32位GUID字符串)");

                    b.Property<string>("AuditorId")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("auditor_id")
                        .HasComment("审核人ID（员工ID）");

                    b.Property<int?>("BatchId")
                        .HasColumnType("int")
                        .HasColumnName("batch_id")
                        .HasComment("关联批次ID");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<int?>("PromotionLinkId")
                        .HasColumnType("int")
                        .HasColumnName("promotion_link_id")
                        .HasComment("推广链接ID");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<byte>("Status")
                        .HasColumnType("tinyint unsigned")
                        .HasColumnName("status")
                        .HasComment("审核状态:0待审核,1通过,2拒绝");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<string>("UserId")
                        .HasColumnType("longtext")
                        .HasColumnName("user_id")
                        .HasComment("被审核用户ID");

                    b.HasKey("Id");

                    b.ToTable("user_audits");
                });

            modelBuilder.Entity("Entity.Entitys.VideoEntity.UserBatchRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AnswerDetails")
                        .HasColumnType("longtext")
                        .HasComment("答题详情(JSON格式)");

                    b.Property<DateTime?>("AnswerTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("答题完成时间");

                    b.Property<int>("BatchId")
                        .HasColumnType("int")
                        .HasComment("批次ID");

                    b.Property<int>("CorrectAnswers")
                        .HasColumnType("int")
                        .HasComment("正确答案数");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("tinyint(1)")
                        .HasComment("是否完播");

                    b.Property<DateTime?>("LastWatchTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("最后观看时间");

                    b.Property<string>("OutTradeNo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("微信支付订单号");

                    b.Property<string>("PromotionLink")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasComment("推广链接");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<decimal>("RewardAmount")
                        .HasColumnType("decimal(10,2)")
                        .HasComment("红包金额");

                    b.Property<string>("RewardFailReason")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("红包发放失败原因");

                    b.Property<int>("RewardRetryCount")
                        .HasColumnType("int")
                        .HasComment("红包发放重试次数");

                    b.Property<byte>("RewardStatus")
                        .HasColumnType("tinyint unsigned")
                        .HasComment("红包状态:0未发放,1发放成功,2发放失败");

                    b.Property<DateTime?>("RewardTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("红包发放时间");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("开始观看时间");

                    b.Property<int>("TotalQuestions")
                        .HasColumnType("int")
                        .HasComment("总题目数");

                    b.Property<string>("TransactionId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("微信支付交易号");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("用户ID");

                    b.Property<int>("ViewDuration")
                        .HasColumnType("int")
                        .HasComment("观看时长(秒)");

                    b.Property<decimal>("WatchProgress")
                        .HasColumnType("decimal(5,4)")
                        .HasComment("观看进度(0-1之间的小数)");

                    b.HasKey("Id");

                    b.HasIndex("BatchId")
                        .HasDatabaseName("IX_UserBatchRecords_BatchId");

                    b.HasIndex("CreateTime")
                        .HasDatabaseName("IX_UserBatchRecords_CreateTime");

                    b.HasIndex("IsCompleted")
                        .HasDatabaseName("IX_UserBatchRecords_IsCompleted");

                    b.HasIndex("RewardStatus")
                        .HasDatabaseName("IX_UserBatchRecords_RewardStatus");

                    b.HasIndex("TotalQuestions")
                        .HasDatabaseName("IX_UserBatchRecords_TotalQuestions");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_UserBatchRecords_UserId");

                    b.HasIndex("UserId", "BatchId")
                        .IsUnique()
                        .HasDatabaseName("IX_UserBatchRecords_UserId_BatchId");

                    b.HasIndex(new[] { "UserId", "BatchId" }, "IX_UserBatchRecord_UserId_BatchId")
                        .IsUnique();

                    b.ToTable("user_batch_records");
                });

            modelBuilder.Entity("Entity.Entitys.VideoEntity.UserTransfer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("FromEmployeeId")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("原员工ID");

                    b.Property<string>("OperatorId")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasComment("操作人ID");

                    b.Property<string>("OperatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("操作人姓名");

                    b.Property<string>("Reason")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("转移原因");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<string>("ToEmployeeId")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("新员工ID");

                    b.Property<DateTime>("TransferTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("转移时间");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasComment("被转移用户ID");

                    b.HasKey("Id");

                    b.ToTable("user_transfers");
                });

            modelBuilder.Entity("Entity.Entitys.VideoEntity.Video", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CoverUrl")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("封面URL");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasComment("视频描述");

                    b.Property<int>("Duration")
                        .HasColumnType("int")
                        .HasComment("视频时长(秒)");

                    b.Property<string>("FileId")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("文件ID，用于关联压缩进度");

                    b.Property<string>("Questions")
                        .HasColumnType("longtext")
                        .HasComment("问题JSON格式 - 格式: [{'questionText':'问题内容','orderNum':0,'options':[{'optionText':'选项1','isCorrect':true,'orderNum':0},{'optionText':'选项2','isCorrect':false,'orderNum':1}]}]");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<decimal>("RewardAmount")
                        .HasColumnType("decimal(10,2)")
                        .HasComment("红包金额");

                    b.Property<byte>("Status")
                        .HasColumnType("tinyint unsigned")
                        .HasComment("状态:0下架,1上架,2失败,3压缩中");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("视频标题");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<string>("VideoUrl")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("视频URL");

                    b.Property<int>("ViewCount")
                        .HasColumnType("int")
                        .HasComment("观看次数");

                    b.HasKey("Id");

                    b.ToTable("videos");
                });

            modelBuilder.Entity("Entity.Entitys.VideoEntity.WechatAccessToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AccessToken")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasComment("AccessToken");

                    b.Property<string>("AppId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("微信AppID");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("datetime(6)")
                        .HasComment("过期时间");

                    b.Property<int?>("ExpiresIn")
                        .HasColumnType("int")
                        .HasComment("过期时间（秒）");

                    b.Property<DateTime?>("ExpiresTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("过期时间戳");

                    b.Property<byte>("IsActive")
                        .HasColumnType("tinyint unsigned")
                        .HasComment("是否激活:0未激活,1已激活");

                    b.Property<byte>("IsExpired")
                        .HasColumnType("tinyint unsigned")
                        .HasComment("是否过期:0未过期,1已过期");

                    b.Property<byte>("IsValid")
                        .HasColumnType("tinyint unsigned")
                        .HasComment("是否有效:0无效,1有效");

                    b.Property<DateTime>("ObtainTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("获取时间");

                    b.Property<string>("RefreshToken")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasComment("刷新Token");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<string>("Scope")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasComment("授权范围");

                    b.Property<string>("TokenType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("Token类型");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.ToTable("wechat_access_tokens");
                });

            modelBuilder.Entity("Entity.Entitys.VideoEntity.WechatPayment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("Amount")
                        .HasColumnType("int")
                        .HasComment("支付金额(分)");

                    b.Property<string>("Body")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasComment("商品描述");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("Currency")
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)")
                        .HasComment("货币类型");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("支付描述");

                    b.Property<string>("FailReason")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("失败原因");

                    b.Property<string>("NotifyUrl")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("通知URL");

                    b.Property<string>("OpenId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("微信OpenID");

                    b.Property<string>("OrderNo")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("订单号");

                    b.Property<string>("OutTradeNo")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("商户订单号");

                    b.Property<DateTime?>("PayTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("支付时间");

                    b.Property<string>("PaymentType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("支付类型");

                    b.Property<int?>("RefundAmount")
                        .HasColumnType("int")
                        .HasComment("退款金额(分)");

                    b.Property<string>("RefundNo")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("退款单号");

                    b.Property<string>("RefundReason")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("退款原因");

                    b.Property<DateTime?>("RefundTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("退款时间");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<int>("RetryCount")
                        .HasColumnType("int")
                        .HasComment("重试次数");

                    b.Property<string>("ReturnUrl")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("返回URL");

                    b.Property<int>("RewardId")
                        .HasColumnType("int")
                        .HasComment("关联红包记录ID");

                    b.Property<byte>("Status")
                        .HasColumnType("tinyint unsigned")
                        .HasComment("支付状态:0待支付,1支付成功,2支付失败");

                    b.Property<string>("Subject")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasComment("商品主题");

                    b.Property<string>("TransactionId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("微信支付交易号");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasComment("用户ID");

                    b.Property<string>("WechatResponse")
                        .HasColumnType("longtext")
                        .HasComment("微信返回信息");

                    b.HasKey("Id");

                    b.ToTable("wechat_payments");
                });
#pragma warning restore 612, 618
        }
    }
}
