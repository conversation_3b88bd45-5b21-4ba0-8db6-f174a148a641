using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.VideoDto
{
    /// <summary>
    /// 用户批次记录查询DTO
    /// </summary>
    public class UserBatchRecordQueryDto : BaseQueryDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        public int? BatchId { get; set; }

        /// <summary>
        /// 是否已完播
        /// </summary>
        public bool? IsCompleted { get; set; }

        /// <summary>
        /// 是否已答题
        /// </summary>
        public bool? HasAnswered { get; set; }

        /// <summary>
        /// 红包状态:0未发放,1发放成功,2发放失败
        /// </summary>
        public byte? RewardStatus { get; set; }

        /// <summary>
        /// 开始时间范围 - 起始
        /// </summary>
        public DateTime? StartTimeFrom { get; set; }

        /// <summary>
        /// 开始时间范围 - 结束
        /// </summary>
        public DateTime? StartTimeTo { get; set; }

        /// <summary>
        /// 员工ID（用于权限过滤）
        /// </summary>
        public string? EmployeeId { get; set; }
    }

    /// <summary>
    /// 用户批次记录创建DTO
    /// </summary>
    public class UserBatchRecordCreateDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        [Required(ErrorMessage = "批次ID不能为空")]
        public int BatchId { get; set; }

        /// <summary>
        /// 用户ID（可为空，匿名用户时由后端自动创建IP用户）
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 推广链接
        /// </summary>
        [MaxLength(500, ErrorMessage = "推广链接长度不能超过500个字符")]
        public string? PromotionLink { get; set; }
    }

    /// <summary>
    /// 观看进度更新DTO
    /// </summary>
    public class WatchProgressUpdateDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        [Required(ErrorMessage = "批次ID不能为空")]
        public int BatchId { get; set; }

        /// <summary>
        /// 观看时长(秒)
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "观看时长必须大于等于0")]
        public int ViewDuration { get; set; }

        /// <summary>
        /// 观看进度(0-1之间的小数)
        /// </summary>
        [Range(0, 1, ErrorMessage = "观看进度必须在0-1之间")]
        public decimal WatchProgress { get; set; }

        /// <summary>
        /// 是否完成观看
        /// </summary>
        public bool IsCompleted { get; set; }
    }

    /// <summary>
    /// 答题结果提交DTO
    /// </summary>
    public class AnswerSubmitDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        [Required(ErrorMessage = "批次ID不能为空")]
        public int BatchId { get; set; }

        /// <summary>
        /// 总题目数
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "总题目数必须大于0")]
        public int TotalQuestions { get; set; }

        /// <summary>
        /// 正确答案数
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "正确答案数必须大于等于0")]
        public int CorrectAnswers { get; set; }

        /// <summary>
        /// 答题详情(JSON格式)
        /// </summary>
        [Required(ErrorMessage = "答题详情不能为空")]
        public string AnswerDetails { get; set; } = string.Empty;
    }

    /// <summary>
    /// 答题提交响应DTO
    /// </summary>
    public class AnswerSubmitResponseDto
    {
        /// <summary>
        /// 是否答题正确（正确率>=50%）
        /// </summary>
        public bool IsCorrect { get; set; }

        /// <summary>
        /// 答题正确率
        /// </summary>
        public decimal CorrectRate { get; set; }

        /// <summary>
        /// 获得的红包金额
        /// </summary>
        public decimal RewardAmount { get; set; }

        /// <summary>
        /// 红包发放状态：0未发放,1发放成功,2发放失败
        /// </summary>
        public byte RewardStatus { get; set; }

        /// <summary>
        /// 提示信息
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 红包发放DTO
    /// </summary>
    public class RewardGrantDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        [Required(ErrorMessage = "批次ID不能为空")]
        public int BatchId { get; set; }

        /// <summary>
        /// 红包金额
        /// </summary>
        [Range(0.01, 200, ErrorMessage = "红包金额必须在0.01-200之间")]
        public decimal RewardAmount { get; set; }

        /// <summary>
        /// 微信支付交易号
        /// </summary>
        [MaxLength(100, ErrorMessage = "微信支付交易号长度不能超过100个字符")]
        public string? TransactionId { get; set; }

        /// <summary>
        /// 微信支付订单号
        /// </summary>
        [MaxLength(100, ErrorMessage = "微信支付订单号长度不能超过100个字符")]
        public string? OutTradeNo { get; set; }
    }

    /// <summary>
    /// 红包状态更新DTO
    /// </summary>
    public class RewardStatusUpdateDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        [Required(ErrorMessage = "批次ID不能为空")]
        public int BatchId { get; set; }

        /// <summary>
        /// 红包状态:0未发放,1发放成功,2发放失败
        /// </summary>
        [Range(0, 2, ErrorMessage = "红包状态必须在0-2之间")]
        public byte RewardStatus { get; set; }

        /// <summary>
        /// 失败原因（状态为2时必填）
        /// </summary>
        [MaxLength(255, ErrorMessage = "失败原因长度不能超过255个字符")]
        public string? FailReason { get; set; }

        /// <summary>
        /// 微信支付交易号
        /// </summary>
        [MaxLength(100, ErrorMessage = "微信支付交易号长度不能超过100个字符")]
        public string? TransactionId { get; set; }

        /// <summary>
        /// 微信支付订单号
        /// </summary>
        [MaxLength(100, ErrorMessage = "微信支付订单号长度不能超过100个字符")]
        public string? OutTradeNo { get; set; }
    }

    /// <summary>
    /// 用户批次记录响应DTO
    /// </summary>
    public class UserBatchRecordResponseDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string? UserNickname { get; set; }

        /// <summary>
        /// 用户头像
        /// </summary>
        public string? UserAvatar { get; set; }

        /// <summary>
        /// 绑定员工ID
        /// </summary>
        public string? EmployeeId { get; set; }

        /// <summary>
        /// 员工姓名
        /// </summary>
        public string? EmployeeName { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        public int BatchId { get; set; }

        /// <summary>
        /// 批次名称
        /// </summary>
        public string? BatchName { get; set; }

        /// <summary>
        /// 视频标题
        /// </summary>
        public string? VideoTitle { get; set; }

        #region 观看相关

        /// <summary>
        /// 观看时长(秒)
        /// </summary>
        public int ViewDuration { get; set; }

        /// <summary>
        /// 观看进度(0-1之间的小数)
        /// </summary>
        public decimal WatchProgress { get; set; }

        /// <summary>
        /// 观看进度百分比
        /// </summary>
        public decimal WatchProgressPercent { get; set; }

        /// <summary>
        /// 是否完播
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 开始观看时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 最后观看时间
        /// </summary>
        public DateTime? LastWatchTime { get; set; }

        #endregion

        #region 答题相关

        /// <summary>
        /// 总题目数
        /// </summary>
        public int TotalQuestions { get; set; }

        /// <summary>
        /// 正确答案数
        /// </summary>
        public int CorrectAnswers { get; set; }

        /// <summary>
        /// 答题正确率
        /// </summary>
        public decimal CorrectRate { get; set; }

        /// <summary>
        /// 是否已答题
        /// </summary>
        public bool HasAnswered { get; set; }

        /// <summary>
        /// 答题时间
        /// </summary>
        public DateTime? AnswerTime { get; set; }

        #endregion

        #region 红包相关

        /// <summary>
        /// 红包金额
        /// </summary>
        public decimal RewardAmount { get; set; }

        /// <summary>
        /// 红包状态:0未发放,1发放成功,2发放失败
        /// </summary>
        public byte RewardStatus { get; set; }

        /// <summary>
        /// 红包状态描述
        /// </summary>
        public string RewardStatusText { get; set; } = string.Empty;

        /// <summary>
        /// 是否已获得红包
        /// </summary>
        public bool HasReward { get; set; }

        /// <summary>
        /// 红包发放时间
        /// </summary>
        public DateTime? RewardTime { get; set; }

        /// <summary>
        /// 红包发放失败原因
        /// </summary>
        public string? RewardFailReason { get; set; }

        #endregion

        /// <summary>
        /// 推广链接
        /// </summary>
        public string? PromotionLink { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }

    /// <summary>
    /// 用户批次记录简要DTO（用于列表显示）
    /// </summary>
    public class UserBatchRecordSummaryDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string? UserNickname { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        public int BatchId { get; set; }

        /// <summary>
        /// 批次名称
        /// </summary>
        public string? BatchName { get; set; }

        /// <summary>
        /// 视频封面URL
        /// </summary>
        public string? VideoCoverUrl { get; set; }

        /// <summary>
        /// 观看进度百分比
        /// </summary>
        public decimal WatchProgressPercent { get; set; }

        /// <summary>
        /// 是否完播
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 是否已答题
        /// </summary>
        public bool HasAnswered { get; set; }

        /// <summary>
        /// 答题正确率
        /// </summary>
        public decimal CorrectRate { get; set; }

        /// <summary>
        /// 总题数
        /// </summary>
        public int TotalQuestions { get; set; }

        /// <summary>
        /// 正确答案数
        /// </summary>
        public int CorrectAnswers { get; set; }

        /// <summary>
        /// 红包金额
        /// </summary>
        public decimal RewardAmount { get; set; }

        /// <summary>
        /// 红包状态:0未发放,1发放成功,2发放失败
        /// </summary>
        public byte RewardStatus { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 批次统计DTO（基于UserBatchRecord）
    /// </summary>
    public class BatchStatisticsFromRecordDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        public int BatchId { get; set; }

        /// <summary>
        /// 批次名称
        /// </summary>
        public string? BatchName { get; set; }

        /// <summary>
        /// 总用户数（所有可访问用户数，按权限过滤）
        /// </summary>
        public int TotalParticipants { get; set; }

        /// <summary>
        /// 观看人数（实际有观看行为的用户数，ViewDuration > 0）
        /// </summary>
        public int ViewerCount { get; set; }

        /// <summary>
        /// 未观看用户数量（总用户数 - 观看用户数）
        /// </summary>
        public int UnwatchedUserCount { get; set; }

        /// <summary>
        /// 完播人数
        /// </summary>
        public int CompletedViewerCount { get; set; }

        /// <summary>
        /// 完播率（完播人数 / 观看人数 * 100）
        /// </summary>
        public decimal CompleteRate { get; set; }

        /// <summary>
        /// 答题人数
        /// </summary>
        public int AnswerCount { get; set; }

        /// <summary>
        /// 答题率（答题人数 / 总用户数 * 100）
        /// </summary>
        public decimal AnswerRate { get; set; }

        /// <summary>
        /// 平均答题正确率
        /// </summary>
        public decimal AverageCorrectRate { get; set; }

        /// <summary>
        /// 红包发放人数
        /// </summary>
        public int RewardCount { get; set; }

        /// <summary>
        /// 红包发放总金额
        /// </summary>
        public decimal TotalRewardAmount { get; set; }

        /// <summary>
        /// 红包发放成功人数
        /// </summary>
        public int SuccessRewardCount { get; set; }

        /// <summary>
        /// 红包发放成功率
        /// </summary>
        public decimal RewardSuccessRate { get; set; }

        /// <summary>
        /// 平均观看时长（秒）
        /// </summary>
        public int AverageViewDuration { get; set; }

        /// <summary>
        /// 平均观看进度
        /// </summary>
        public decimal AverageWatchProgress { get; set; }
    }

    /// <summary>
    /// 观看状态DTO
    /// </summary>
    public class WatchStatusDto
    {
        /// <summary>
        /// 是否有记录
        /// </summary>
        public bool HasRecord { get; set; }

        /// <summary>
        /// 是否已观看
        /// </summary>
        public bool HasWatched { get; set; }

        /// <summary>
        /// 是否完播
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 观看进度(0-1之间的小数)
        /// </summary>
        public decimal WatchProgress { get; set; }

        /// <summary>
        /// 观看进度百分比
        /// </summary>
        public decimal WatchProgressPercent { get; set; }

        /// <summary>
        /// 观看时长(秒)
        /// </summary>
        public int ViewDuration { get; set; }

        /// <summary>
        /// 开始观看时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 最后观看时间
        /// </summary>
        public DateTime? LastWatchTime { get; set; }
    }

    /// <summary>
    /// 批量观看进度更新DTO
    /// </summary>
    public class BatchWatchProgressUpdateDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public string UserId { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        [Required(ErrorMessage = "批次ID不能为空")]
        public int BatchId { get; set; }

        /// <summary>
        /// 观看时长(秒)
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "观看时长必须大于等于0")]
        public int ViewDuration { get; set; }

        /// <summary>
        /// 观看进度(0-1之间的小数)
        /// </summary>
        [Range(0, 1, ErrorMessage = "观看进度必须在0-1之间")]
        public decimal WatchProgress { get; set; }

        /// <summary>
        /// 是否完成观看
        /// </summary>
        public bool IsCompleted { get; set; }
    }

    /// <summary>
    /// 答题状态DTO
    /// </summary>
    public class AnswerStatusDto
    {
        /// <summary>
        /// 是否有记录
        /// </summary>
        public bool HasRecord { get; set; }

        /// <summary>
        /// 是否已答题
        /// </summary>
        public bool HasAnswered { get; set; }

        /// <summary>
        /// 总题目数
        /// </summary>
        public int TotalQuestions { get; set; }

        /// <summary>
        /// 正确答案数
        /// </summary>
        public int CorrectAnswers { get; set; }

        /// <summary>
        /// 答题正确率
        /// </summary>
        public decimal CorrectRate { get; set; }

        /// <summary>
        /// 答题时间
        /// </summary>
        public DateTime? AnswerTime { get; set; }

        /// <summary>
        /// 红包金额
        /// </summary>
        public decimal RewardAmount { get; set; }

        /// <summary>
        /// 红包状态:0未发放,1发放成功,2发放失败
        /// </summary>
        public byte RewardStatus { get; set; }

        /// <summary>
        /// 红包状态描述
        /// </summary>
        public string RewardStatusText { get; set; } = string.Empty;

        /// <summary>
        /// 答题详情(JSON格式)
        /// </summary>
        public string? AnswerDetails { get; set; }
    }

    /// <summary>
    /// 答题详情DTO
    /// </summary>
    public class AnswerDetailDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string? UserNickname { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        public int BatchId { get; set; }

        /// <summary>
        /// 批次名称
        /// </summary>
        public string? BatchName { get; set; }

        /// <summary>
        /// 总题目数
        /// </summary>
        public int TotalQuestions { get; set; }

        /// <summary>
        /// 正确答案数
        /// </summary>
        public int CorrectAnswers { get; set; }

        /// <summary>
        /// 答题正确率
        /// </summary>
        public decimal CorrectRate { get; set; }

        /// <summary>
        /// 答题详情(JSON格式)
        /// </summary>
        public string? AnswerDetails { get; set; }

        /// <summary>
        /// 答题时间
        /// </summary>
        public DateTime? AnswerTime { get; set; }
    }

    /// <summary>
    /// 答题资格验证DTO
    /// </summary>
    public class AnswerEligibilityDto
    {
        /// <summary>
        /// 是否可以答题
        /// </summary>
        public bool CanAnswer { get; set; }

        /// <summary>
        /// 原因说明
        /// </summary>
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// 红包状态DTO
    /// </summary>
    public class RewardStatusDto
    {
        /// <summary>
        /// 是否有记录
        /// </summary>
        public bool HasRecord { get; set; }

        /// <summary>
        /// 是否已获得红包
        /// </summary>
        public bool HasReward { get; set; }

        /// <summary>
        /// 是否可以领取红包
        /// </summary>
        public bool CanReceiveReward { get; set; }

        /// <summary>
        /// 红包金额
        /// </summary>
        public decimal RewardAmount { get; set; }

        /// <summary>
        /// 红包状态:0未发放,1发放成功,2发放失败
        /// </summary>
        public byte RewardStatus { get; set; }

        /// <summary>
        /// 红包状态描述
        /// </summary>
        public string RewardStatusText { get; set; } = string.Empty;

        /// <summary>
        /// 红包发放时间
        /// </summary>
        public DateTime? RewardTime { get; set; }

        /// <summary>
        /// 红包发放失败原因
        /// </summary>
        public string? RewardFailReason { get; set; }

        /// <summary>
        /// 红包发放重试次数
        /// </summary>
        public int RewardRetryCount { get; set; }

        /// <summary>
        /// 微信支付交易号
        /// </summary>
        public string? TransactionId { get; set; }

        /// <summary>
        /// 微信支付订单号
        /// </summary>
        public string? OutTradeNo { get; set; }
    }

    /// <summary>
    /// 红包发放资格验证DTO
    /// </summary>
    public class RewardEligibilityDto
    {
        /// <summary>
        /// 是否可以发放红包
        /// </summary>
        public bool CanReceiveReward { get; set; }

        /// <summary>
        /// 原因说明
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 建议红包金额
        /// </summary>
        public decimal SuggestedAmount { get; set; }
    }

    /// <summary>
    /// 批量红包发放结果DTO
    /// </summary>
    public class BatchRewardResultDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        public int BatchId { get; set; }

        /// <summary>
        /// 符合条件的总人数
        /// </summary>
        public int TotalEligible { get; set; }

        /// <summary>
        /// 成功发放人数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 发放失败人数
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 发放失败的用户列表
        /// </summary>
        public List<string> FailedUsers { get; set; } = [];

        /// <summary>
        /// 成功率
        /// </summary>
        public decimal SuccessRate => TotalEligible > 0 ? Math.Round((decimal)SuccessCount / TotalEligible * 100, 2) : 0;
    }

    /// <summary>
    /// 访问推广链接DTO
    /// </summary>
    public class AccessPromotionDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        [Required(ErrorMessage = "批次ID不能为空")]
        public int BatchId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 推广链接
        /// </summary>
        public string? PromotionLink { get; set; }

        /// <summary>
        /// 访问来源
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        public string? EmployeeId { get; set; }

        /// <summary>
        /// 推广码
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string? Message { get; set; }
    }

    /// <summary>
    /// 访问推广链接响应DTO
    /// </summary>
    public class AccessPromotionResponseDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 用户批次记录ID
        /// </summary>
        public int? RecordId { get; set; }

        /// <summary>
        /// 是否是新记录
        /// </summary>
        public bool IsNewRecord { get; set; }

        /// <summary>
        /// 是否需要审核
        /// </summary>
        public bool NeedAudit { get; set; }

        /// <summary>
        /// 审核状态
        /// </summary>
        public string? AuditStatus { get; set; }

        /// <summary>
        /// 批次信息
        /// </summary>
        public object? Batch { get; set; }

        /// <summary>
        /// 用户信息
        /// </summary>
        public object? User { get; set; }
    }




}
