using Common.Autofac;
using DAL.VideoDAL;
using Entity.Dto.VideoDto;

namespace BLL.VideoService
{
    /// <summary>
    /// 订单统计服务
    /// 基于微信支付记录(WechatPayment)实现订单统计功能
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class OrderStatisticsService(WechatPaymentDAL wechatPaymentDAL)
    {
        private readonly WechatPaymentDAL _wechatPaymentDAL = wechatPaymentDAL;

        /// <summary>
        /// 获取订单统计信息
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>订单统计</returns>
        public async Task<OrderStatisticsDto> GetOrderStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            // 获取时间范围内的所有支付记录
            var payments = await _wechatPaymentDAL.GetPaymentsByDateRangeAsync(startDate, endDate);

            var totalCount = payments.Count;
            var successCount = payments.Count(p => p.Status == 1);
            var failedCount = payments.Count(p => p.Status == 2);
            var pendingCount = payments.Count(p => p.Status == 0);

            var totalAmount = payments.Sum(p => p.Amount) / 100m; // 转换为元
            var successAmount = payments.Where(p => p.Status == 1).Sum(p => p.Amount) / 100m;

            var averageAmount = totalCount > 0 ? totalAmount / totalCount : 0;
            var successRate = totalCount > 0 ? (decimal)successCount / totalCount * 100 : 0;

            return new OrderStatisticsDto
            {
                TotalCount = totalCount,
                SuccessCount = successCount,
                FailedCount = failedCount,
                PendingCount = pendingCount,
                TotalAmount = totalAmount,
                SuccessAmount = successAmount,
                AverageAmount = averageAmount,
                SuccessRate = successRate,
                StartDate = startDate,
                EndDate = endDate
            };
        }

        /// <summary>
        /// 获取今日订单统计
        /// </summary>
        /// <returns>今日订单统计</returns>
        public async Task<OrderStatisticsDto> GetTodayOrderStatisticsAsync()
        {
            var today = DateTime.Today;
            var todayEnd = today.AddDays(1).AddSeconds(-1);
            return await GetOrderStatisticsAsync(today, todayEnd);
        }

        /// <summary>
        /// 获取订单总数
        /// </summary>
        /// <returns>订单总数</returns>
        public async Task<int> GetTotalOrderCountAsync()
        {
            var allPayments = await _wechatPaymentDAL.GetAllPaymentsAsync();
            return allPayments.Count;
        }

        /// <summary>
        /// 获取今日订单数
        /// </summary>
        /// <returns>今日订单数</returns>
        public async Task<int> GetTodayOrderCountAsync()
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            var todayPayments = await _wechatPaymentDAL.GetPaymentsByDateRangeAsync(today, tomorrow.AddSeconds(-1));
            return todayPayments.Count;
        }

        /// <summary>
        /// 获取订单总金额
        /// </summary>
        /// <returns>订单总金额（元）</returns>
        public async Task<decimal> GetTotalOrderAmountAsync()
        {
            var allPayments = await _wechatPaymentDAL.GetAllPaymentsAsync();
            return allPayments.Sum(p => p.Amount) / 100m; // 转换为元
        }

        /// <summary>
        /// 获取今日订单金额
        /// </summary>
        /// <returns>今日订单金额（元）</returns>
        public async Task<decimal> GetTodayOrderAmountAsync()
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            var todayPayments = await _wechatPaymentDAL.GetPaymentsByDateRangeAsync(today, tomorrow.AddSeconds(-1));
            return todayPayments.Sum(p => p.Amount) / 100m; // 转换为元
        }

        /// <summary>
        /// 获取订单统计对比数据
        /// </summary>
        /// <param name="currentStartDate">当前开始时间</param>
        /// <param name="currentEndDate">当前结束时间</param>
        /// <param name="previousStartDate">对比开始时间</param>
        /// <param name="previousEndDate">对比结束时间</param>
        /// <returns>订单统计对比</returns>
        public async Task<(OrderStatisticsDto Current, OrderStatisticsDto Previous)> GetOrderStatisticsCompareAsync(
            DateTime currentStartDate, DateTime currentEndDate, DateTime previousStartDate, DateTime previousEndDate)
        {
            var currentStats = await GetOrderStatisticsAsync(currentStartDate, currentEndDate);
            var previousStats = await GetOrderStatisticsAsync(previousStartDate, previousEndDate);

            return (currentStats, previousStats);
        }

        /// <summary>
        /// 获取按支付类型分组的订单统计
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns>按支付类型分组的订单统计</returns>
        public async Task<Dictionary<string, OrderStatisticsDto>> GetOrderStatisticsByPaymentTypeAsync(DateTime startDate, DateTime endDate)
        {
            var payments = await _wechatPaymentDAL.GetPaymentsByDateRangeAsync(startDate, endDate);
            var groupedPayments = payments.GroupBy(p => p.PaymentType ?? "UNKNOWN");

            var result = new Dictionary<string, OrderStatisticsDto>();

            foreach (var group in groupedPayments)
            {
                var paymentList = group.ToList();
                var totalCount = paymentList.Count;
                var successCount = paymentList.Count(p => p.Status == 1);
                var failedCount = paymentList.Count(p => p.Status == 2);
                var pendingCount = paymentList.Count(p => p.Status == 0);

                var totalAmount = paymentList.Sum(p => p.Amount) / 100m;
                var successAmount = paymentList.Where(p => p.Status == 1).Sum(p => p.Amount) / 100m;

                var averageAmount = totalCount > 0 ? totalAmount / totalCount : 0;
                var successRate = totalCount > 0 ? (decimal)successCount / totalCount * 100 : 0;

                result[group.Key] = new OrderStatisticsDto
                {
                    TotalCount = totalCount,
                    SuccessCount = successCount,
                    FailedCount = failedCount,
                    PendingCount = pendingCount,
                    TotalAmount = totalAmount,
                    SuccessAmount = successAmount,
                    AverageAmount = averageAmount,
                    SuccessRate = successRate,
                    StartDate = startDate,
                    EndDate = endDate
                };
            }

            return result;
        }
    }
}
