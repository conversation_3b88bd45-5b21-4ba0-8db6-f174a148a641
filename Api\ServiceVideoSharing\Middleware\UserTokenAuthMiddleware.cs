using BLL.Common;
using System.Text.Json;

namespace ServiceVideoSharing.Middleware
{
    /// <summary>
    /// 用户端Token验证中间件
    /// 专门用于验证微信用户的业务token，与管理系统权限验证完全独立
    /// </summary>
    public class UserTokenAuthMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<UserTokenAuthMiddleware> _logger;

        public UserTokenAuthMiddleware(RequestDelegate next, ILogger<UserTokenAuthMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // 检查是否是需要用户端token验证的路径
            if (IsUserApiPath(context.Request.Path))
            {
                _logger.LogInformation("用户端API请求 - Path: {Path}, Method: {Method}",
                    context.Request.Path, context.Request.Method);

                var token = ExtractUserToken(context.Request);
                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogWarning("用户端API请求缺少Token - Path: {Path}", context.Request.Path);
                    await WriteUnauthorizedResponse(context, "缺少用户Token");
                    return;
                }

                var (isValid, principal, errorMessage) = UserJWTHelper.ValidateUserToken(token);
                if (!isValid)
                {
                    _logger.LogWarning("用户端Token验证失败 - Path: {Path}, Error: {Error}",
                        context.Request.Path, errorMessage);
                    await WriteUnauthorizedResponse(context, errorMessage ?? "Token验证失败");
                    return;
                }

                // 检查用户审核状态（只有审核通过的用户才能使用业务功能）
                var auditStatus = UserJWTHelper.GetAuditStatusFromToken(token);
                if (auditStatus != 1) // 只有状态为1（已通过）的用户才能使用功能
                {
                    var statusText = auditStatus switch
                    {
                        0 => "待审核",
                        2 => "审核被拒绝",
                        _ => "未知状态"
                    };
                    _logger.LogWarning("用户账户{Status} - Path: {Path}, UserId: {UserId}",
                        statusText, context.Request.Path, principal?.Identity?.Name);
                    await WriteUnauthorizedResponse(context, $"账户{statusText}，暂无法使用此功能");
                    return;
                }

                // 将用户信息添加到HttpContext，供后续使用
                context.Items["UserId"] = UserJWTHelper.GetUserIdFromToken(token);
                context.Items["AuditStatus"] = auditStatus;
                context.Items["InviterId"] = UserJWTHelper.GetInviterIdFromToken(token);
                context.Items["UserPrincipal"] = principal;

                _logger.LogInformation("用户端Token验证成功 - UserId: {UserId}, AuditStatus: {AuditStatus}",
                    context.Items["UserId"], auditStatus);
            }

            await _next(context);
        }

        /// <summary>
        /// 判断是否是用户端API路径
        /// </summary>
        /// <param name="path">请求路径</param>
        /// <returns>是否需要用户端token验证</returns>
        private static bool IsUserApiPath(PathString path)
        {
            // 定义需要用户端token验证的API路径
            var userApiPaths = new[]
            {
                "/api/user/video",      // 观看视频相关
                "/api/user/progress",   // 观看进度相关
                "/api/user/quiz",       // 答题相关
                "/api/user/redpacket",  // 红包相关
                "/api/user/profile"     // 用户信息相关
            };

            return userApiPaths.Any(apiPath => path.StartsWithSegments(apiPath, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 从请求中提取用户Token
        /// </summary>
        /// <param name="request">HTTP请求</param>
        /// <returns>用户Token</returns>
        private static string? ExtractUserToken(HttpRequest request)
        {
            // 优先从Header中获取
            if (request.Headers.TryGetValue("User-Token", out var headerToken))
            {
                return headerToken.FirstOrDefault();
            }

            // 其次从Authorization Header中获取（Bearer格式）
            if (request.Headers.TryGetValue("Authorization", out var authHeader))
            {
                var authValue = authHeader.FirstOrDefault();
                if (!string.IsNullOrEmpty(authValue) && authValue.StartsWith("UserBearer ", StringComparison.OrdinalIgnoreCase))
                {
                    return authValue["UserBearer ".Length..];
                }
            }

            // 最后从Query参数中获取
            if (request.Query.TryGetValue("userToken", out var queryToken))
            {
                return queryToken.FirstOrDefault();
            }

            return null;
        }

        /// <summary>
        /// 写入未授权响应
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="message">错误消息</param>
        private async Task WriteUnauthorizedResponse(HttpContext context, string message)
        {
            context.Response.StatusCode = 401;
            context.Response.ContentType = "application/json; charset=utf-8";

            var response = new
            {
                success = false,
                message = message,
                code = 401,
                timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            };

            var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            await context.Response.WriteAsync(jsonResponse);
        }
    }

    /// <summary>
    /// 用户Token验证中间件扩展方法
    /// </summary>
    public static class UserTokenAuthMiddlewareExtensions
    {
        /// <summary>
        /// 添加用户Token验证中间件
        /// </summary>
        /// <param name="builder">应用程序构建器</param>
        /// <returns>应用程序构建器</returns>
        public static IApplicationBuilder UseUserTokenAuth(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<UserTokenAuthMiddleware>();
        }
    }
}

/// <summary>
/// HttpContext扩展方法，方便获取用户信息
/// </summary>
public static class HttpContextUserExtensions
{
    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <returns>用户ID</returns>
    public static string? GetCurrentUserId(this HttpContext context)
    {
        return context.Items["UserId"]?.ToString();
    }

    /// <summary>
    /// 获取当前用户审核状态
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <returns>审核状态</returns>
    public static int? GetCurrentUserAuditStatus(this HttpContext context)
    {
        return context.Items["AuditStatus"] as int?;
    }

    /// <summary>
    /// 获取当前用户邀请人ID
    /// </summary>
    /// <param name="context">HTTP上下文</param>
    /// <returns>邀请人ID</returns>
    public static string? GetCurrentUserInviterId(this HttpContext context)
    {
        return context.Items["InviterId"]?.ToString();
    }
}
