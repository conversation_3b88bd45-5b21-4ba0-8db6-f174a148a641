using BLL.VideoService;
using Common;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 微信相关接口控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class WechatController(WechatOAuthService wechatOAuthService, WechatAccessTokenService wechatAccessTokenService) : BaseController
    {
        private readonly WechatOAuthService _wechatOAuthService = wechatOAuthService;
        private readonly WechatAccessTokenService _wechatAccessTokenService = wechatAccessTokenService;

        /// <summary>
        /// 获取微信OAuth2.0授权URL
        /// </summary>
        /// <param name="requestDto">授权请求参数</param>
        /// <returns>授权URL</returns>
        [HttpGet("authorize")]
        public async Task<Result<WechatOAuthUrlResponseDto>> GetOAuthUrl([FromQuery] WechatOAuthRequestDto requestDto)
        {
            if (!ModelState.IsValid)
                return Fail<WechatOAuthUrlResponseDto>("参数验证失败", 400);

            // 验证邀请人身份
            var (isValid, _) = await _wechatOAuthService.ValidateInviterAsync(requestDto.InviterId);
            if (!isValid)
                return Fail<WechatOAuthUrlResponseDto>("邀请人异常");

            var authUrl = _wechatOAuthService.GenerateOAuthUrl(requestDto);

            var response = new WechatOAuthUrlResponseDto
            {
                AuthUrl = authUrl,
                InviterId = requestDto.InviterId
            };

            return Success(response, "获取授权URL成功");
        }

        /// <summary>
        /// 检查微信配置
        /// </summary>
        /// <returns>配置状态</returns>
        [HttpGet("config-check")]
        public Result<WechatConfigCheckResponseDto> CheckConfig()
        {
            var appId = WxSetting.AppId;
            var appSecret = WxSetting.AppSecret;

            var response = new WechatConfigCheckResponseDto
            {
                AppId = appId,
                AppSecretConfigured = !string.IsNullOrEmpty(appSecret),
                AppSecretLength = appSecret?.Length ?? 0
            };

            return Success(response, "配置检查完成");
        }

        /// <summary>
        /// 微信OAuth2.0授权回调接口
        /// </summary>
        /// <param name="callbackDto">回调参数</param>
        /// <returns>登录结果</returns>
        [HttpGet("callback")]
        public async Task<Result<WechatOAuthLoginResponseDto>> OAuthCallback([FromQuery] WechatOAuthCallbackDto callbackDto)
        {
            if (!ModelState.IsValid)
                return Fail<WechatOAuthLoginResponseDto>("参数验证失败", 400);

            var loginResult = await _wechatOAuthService.HandleOAuthCallbackAsync(callbackDto);

            // 根据审核状态确定消息
            string message;
            if (loginResult.IsNewUser)
            {
                message = loginResult.AuditStatus == 1 ? "新用户注册并登录成功" : "新用户注册成功，等待审核";
            }
            else
            {
                message = loginResult.AuditStatus == 1 ? "用户登录成功" : "用户登录成功，但账户待审核";
            }

            return Success(loginResult, message);
        }

        /// <summary>
        /// 手动刷新微信基础access_token
        /// </summary>
        /// <returns>刷新结果</returns>
        [HttpPost("token/refresh")]
        public async Task<Result> RefreshBasicAccessToken()
        {
            var success = await _wechatOAuthService.RefreshBasicAccessTokenAsync();

            return success
                ? Success("微信基础access_token刷新成功")
                : Fail("微信基础access_token刷新失败");
        }

        /// <summary>
        /// 获取当前微信基础access_token状态
        /// </summary>
        /// <returns>Token状态</returns>
        [HttpGet("token/status")]
        public async Task<Result<WechatTokenStatusResponseDto>> GetTokenStatus()
        {
            var appId = WxSetting.AppId;
            if (string.IsNullOrEmpty(appId))
                return Fail<WechatTokenStatusResponseDto>("微信AppID未配置", 400);

            var token = await _wechatAccessTokenService.GetValidAccessTokenAsync(appId, "basic");

            var response = new WechatTokenStatusResponseDto();

            if (token != null)
            {
                response.HasValidToken = true;
                response.AccessToken = token;
                return Success(response, "Token状态正常");
            }
            else
            {
                response.HasValidToken = false;
                response.ExpiresAt = null;
                response.RemainingMinutes = 0;
                return Success(response, "无有效Token");
            }
        }

        /// <summary>
        /// 微信服务器验证接口（用于配置微信公众号）
        /// </summary>
        /// <param name="signature">微信加密签名</param>
        /// <param name="timestamp">时间戳</param>
        /// <param name="nonce">随机数</param>
        /// <param name="echostr">随机字符串</param>
        /// <returns>验证结果</returns>
        [HttpGet("verify")]
        public IActionResult VerifyWechatServer(string signature, string timestamp, string nonce, string echostr)
        {
            // TODO: 生产环境需要验证signature
            if (string.IsNullOrEmpty(echostr))
                return BadRequest("Invalid verification request");

            return Content(echostr, "text/plain");
        }
    }
}
