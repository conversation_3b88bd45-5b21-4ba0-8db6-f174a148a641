using Microsoft.AspNetCore.Mvc;

namespace ServiceVideoSharing.Controllers.UserControllers
{
    /// <summary>
    /// 用户端视频相关API控制器
    /// 使用用户端Token验证，与管理系统API完全独立
    /// </summary>
    [ApiController]
    [Route("api/user/video")]
    public class UserVideoController : ControllerBase
    {
        private readonly ILogger<UserVideoController> _logger;

        public UserVideoController(ILogger<UserVideoController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 获取用户可观看的视频列表
        /// </summary>
        /// <returns>视频列表</returns>
        [HttpGet("list")]
        public IActionResult GetVideoList()
        {
            _logger.LogInformation("=== 用户获取视频列表 ===");

            // 从HttpContext中获取用户信息（由UserTokenAuthMiddleware设置）
            var userId = HttpContext.GetCurrentUserId();
            var auditStatus = HttpContext.GetCurrentUserAuditStatus();
            var inviterId = HttpContext.GetCurrentUserInviterId();

            _logger.LogInformation("用户信息 - UserId: {UserId}, AuditStatus: {AuditStatus}, InviterId: {InviterId}",
                userId, auditStatus, inviterId);

            // 这里可以根据用户的审核状态、邀请人等信息来过滤视频
            var videoList = new[]
            {
                new
                {
                    id = "video_001",
                    title = "示例视频1",
                    duration = 300,
                    thumbnail = "https://example.com/thumb1.jpg",
                    canWatch = auditStatus == 1 // 只有审核通过的用户才能观看
                },
                new
                {
                    id = "video_002",
                    title = "示例视频2",
                    duration = 450,
                    thumbnail = "https://example.com/thumb2.jpg",
                    canWatch = auditStatus == 1
                }
            };

            return Ok(new
            {
                success = true,
                data = new
                {
                    videos = videoList,
                    userInfo = new
                    {
                        userId,
                        auditStatus,
                        inviterId,
                        canWatchVideos = auditStatus == 1
                    }
                },
                message = "获取视频列表成功"
            });
        }

        /// <summary>
        /// 记录视频观看进度
        /// </summary>
        /// <param name="videoId">视频ID</param>
        /// <param name="progress">观看进度（秒）</param>
        /// <returns>记录结果</returns>
        [HttpPost("{videoId}/progress")]
        public IActionResult RecordProgress(string videoId, [FromBody] int progress)
        {
            _logger.LogInformation("=== 记录视频观看进度 ===");

            var userId = HttpContext.GetCurrentUserId();
            var auditStatus = HttpContext.GetCurrentUserAuditStatus();

            _logger.LogInformation("记录进度 - UserId: {UserId}, VideoId: {VideoId}, Progress: {Progress}秒",
                userId, videoId, progress);

            // 检查用户是否有权限观看视频
            if (auditStatus != 1)
            {
                return BadRequest(new
                {
                    success = false,
                    message = "账户未通过审核，无法观看视频"
                });
            }

            // 这里实现具体的进度记录逻辑
            // 可以保存到数据库等

            return Ok(new
            {
                success = true,
                data = new
                {
                    videoId,
                    progress,
                    userId,
                    recordTime = DateTime.Now
                },
                message = "观看进度记录成功"
            });
        }
    }
}

/// <summary>
/// 用户端答题API控制器
/// </summary>
[ApiController]
[Route("api/user/quiz")]
public class UserQuizController : ControllerBase
{
    private readonly ILogger<UserQuizController> _logger;

    public UserQuizController(ILogger<UserQuizController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 获取答题列表
    /// </summary>
    /// <returns>答题列表</returns>
    [HttpGet("list")]
    public IActionResult GetQuizList()
    {
        _logger.LogInformation("=== 用户获取答题列表 ===");

        var userId = HttpContext.GetCurrentUserId();
        var auditStatus = HttpContext.GetCurrentUserAuditStatus();

        _logger.LogInformation("用户信息 - UserId: {UserId}, AuditStatus: {AuditStatus}", userId, auditStatus);

        if (auditStatus != 1)
        {
            return BadRequest(new
            {
                success = false,
                message = "账户未通过审核，无法参与答题"
            });
        }

        var quizList = new[]
        {
            new
            {
                id = "quiz_001",
                title = "示例答题1",
                questions = 10,
                timeLimit = 600,
                reward = 5.0
            }
        };

        return Ok(new
        {
            success = true,
            data = quizList,
            message = "获取答题列表成功"
        });
    }

    /// <summary>
    /// 提交答题结果
    /// </summary>
    /// <param name="quizId">答题ID</param>
    /// <param name="answers">答案列表</param>
    /// <returns>答题结果</returns>
    [HttpPost("{quizId}/submit")]
    public IActionResult SubmitQuiz(string quizId, [FromBody] string[] answers)
    {
        _logger.LogInformation("=== 提交答题结果 ===");

        var userId = HttpContext.GetCurrentUserId();
        _logger.LogInformation("提交答题 - UserId: {UserId}, QuizId: {QuizId}, AnswerCount: {Count}",
            userId, quizId, answers?.Length ?? 0);

        // 这里实现具体的答题逻辑

        return Ok(new
        {
            success = true,
            data = new
            {
                quizId,
                userId,
                score = 85,
                passed = true,
                reward = 5.0,
                submitTime = DateTime.Now
            },
            message = "答题提交成功"
        });
    }
}

/// <summary>
/// 用户端红包API控制器
/// </summary>
[ApiController]
[Route("api/user/redpacket")]
public class UserRedPacketController : ControllerBase
{
    private readonly ILogger<UserRedPacketController> _logger;

    public UserRedPacketController(ILogger<UserRedPacketController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 获取可领取的红包列表
    /// </summary>
    /// <returns>红包列表</returns>
    [HttpGet("available")]
    public IActionResult GetAvailableRedPackets()
    {
        _logger.LogInformation("=== 获取可领取红包列表 ===");

        var userId = HttpContext.GetCurrentUserId();
        var auditStatus = HttpContext.GetCurrentUserAuditStatus();
        var inviterId = HttpContext.GetCurrentUserInviterId();

        _logger.LogInformation("用户信息 - UserId: {UserId}, AuditStatus: {AuditStatus}, InviterId: {InviterId}",
            userId, auditStatus, inviterId);

        if (auditStatus != 1)
        {
            return BadRequest(new
            {
                success = false,
                message = "账户未通过审核，无法领取红包"
            });
        }

        var redPackets = new[]
        {
            new
            {
                id = "rp_001",
                type = "新用户红包",
                amount = 10.0,
                condition = "完成注册",
                canClaim = true
            },
            new
            {
                id = "rp_002",
                type = "邀请奖励",
                amount = 5.0,
                condition = "邀请好友注册",
                canClaim = !string.IsNullOrEmpty(inviterId)
            }
        };

        return Ok(new
        {
            success = true,
            data = redPackets,
            message = "获取红包列表成功"
        });
    }

    /// <summary>
    /// 领取红包
    /// </summary>
    /// <param name="redPacketId">红包ID</param>
    /// <returns>领取结果</returns>
    [HttpPost("{redPacketId}/claim")]
    public IActionResult ClaimRedPacket(string redPacketId)
    {
        _logger.LogInformation("=== 领取红包 ===");

        var userId = HttpContext.GetCurrentUserId();
        _logger.LogInformation("领取红包 - UserId: {UserId}, RedPacketId: {RedPacketId}", userId, redPacketId);

        // 这里实现具体的红包领取逻辑

        return Ok(new
        {
            success = true,
            data = new
            {
                redPacketId,
                userId,
                amount = 10.0,
                claimTime = DateTime.Now,
                balance = 15.0
            },
            message = "红包领取成功"
        });
    }
}
