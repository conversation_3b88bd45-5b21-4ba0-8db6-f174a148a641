﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DAL.Migrations
{
    /// <inheritdoc />
    public partial class _4 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RedPacketAmount",
                table: "batches");

            migrationBuilder.AlterColumn<decimal>(
                name: "RewardAmount",
                table: "batches",
                type: "decimal(10,2)",
                nullable: false,
                comment: "红包金额",
                oldClrType: typeof(decimal),
                oldType: "decimal(10,2)",
                oldComment: "红包金额(冗余)");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "RewardAmount",
                table: "batches",
                type: "decimal(10,2)",
                nullable: false,
                comment: "红包金额(冗余)",
                oldClrType: typeof(decimal),
                oldType: "decimal(10,2)",
                oldComment: "红包金额");

            migrationBuilder.AddColumn<decimal>(
                name: "RedPacketAmount",
                table: "batches",
                type: "decimal(10,2)",
                nullable: false,
                defaultValue: 0m,
                comment: "红包金额");
        }
    }
}
